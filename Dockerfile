FROM node:20-alpine AS base

RUN apk add --no-cache nginx git

# No longer need legacy OpenSSL provider for Vue 3 + Vite

RUN mkdir -p /tmp/nginx/praeco && \
    mkdir -p /var/log/nginx && \
    mkdir -p /var/www/html
WORKDIR /tmp/nginx/praeco
COPY package.json .

FROM base AS dependencies
RUN npm install --legacy-peer-deps --loglevel error

FROM base AS release
COPY --from=dependencies /tmp/nginx/praeco/node_modules ./node_modules
COPY . .

RUN npm run build
RUN cp -r dist/* /var/www/html
EXPOSE 8080
ENTRYPOINT ["./entrypoint.sh"]
