---
name: Bug report
about: If you do not provide enough information, this issue may become obsolete and eventually be removed.
labels: bug

---

## 🐛 Describe the bug

- Docker image and tag names for praeco and elastalert
Example)
praecoapp/praeco:1.8.6
praecoapp/elastalert-server:20210517

- Screen capture of praeco operation

- Rules generated by praeco

- Please provide if there is an error in elastalert-server.

## 👀 Steps to reproduce

(Bug reproduction procedure)

Please write as much as you can remember the detailed procedure. This is because I want to check if it can be reproduced in the same way.

1. Do action
2. Do another action
3. Wrong Behavior !!

## 🆗 Expected behavior

(State required to be Closed)

## Screenshots


## Environments


## Additional context

