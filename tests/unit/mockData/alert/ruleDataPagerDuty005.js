export const ruleYaml = `__praeco_query_builder: '{"query":{"logicalOperator":"all","children":[]}}'
alert:
  - pagerduty
alert_subject: this is a test subject
alert_subject_args: []
alert_text: this is a test body
alert_text_args: []
alert_text_type: alert_text_only
doc_type: syslog
filter:
  - query:
      query_string:
        query: '@timestamp:*'
import: BaseRule.config
index: hannibal-*
is_enabled: false
name: test123
num_events: 10000
pagerduty_api_version: v2
pagerduty_ca_certs: true
pagerduty_ignore_ssl_errors: true
pagerduty_v2_payload_class_args:
  - a
pagerduty_v2_payload_component_args:
  - b
pagerduty_v2_payload_group_args:
  - c
pagerduty_v2_payload_severity: error
pagerduty_v2_payload_source: f
realert:
  minutes: 5
timeframe:
  minutes: 5
timestamp_field: '@timestamp'
timestamp_type: iso
type: frequency
use_count_query: true
use_strftime_index: false
`;
