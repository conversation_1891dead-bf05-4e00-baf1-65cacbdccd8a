{"total": 117050, "max_score": null, "hits": [{"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T03:25:57.570936Z", "alert_sent": true, "match_body": {"reference_count": 0, "num_hits": 0, "@timestamp": "2019-02-16T03:24:57.288584Z", "spike_count": 0, "num_matches": 1}, "rule_name": "test1", "match_time": "2019-02-16T03:24:57.288584Z", "alert_time": "2019-02-16T03:25:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T03:15:57.399106Z", "alert_sent": true, "match_body": {"reference_count": 0, "num_hits": 0, "@timestamp": "2019-02-16T03:14:57.166775Z", "spike_count": 0, "num_matches": 1}, "rule_name": "test1", "match_time": "2019-02-16T03:14:57.166775Z", "alert_time": "2019-02-16T03:15:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T03:04:59.352959Z", "alert_sent": true, "match_body": {"reference_count": 0, "num_hits": 0, "@timestamp": "2019-02-16T03:03:57.394397Z", "spike_count": 0, "num_matches": 1}, "rule_name": "test1", "match_time": "2019-02-16T03:03:57.394397Z", "alert_time": "2019-02-16T03:04:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:54:57.443745Z", "alert_sent": true, "match_body": {"reference_count": 0, "num_hits": 0, "@timestamp": "2019-02-16T02:53:57.142648Z", "spike_count": 0, "num_matches": 1}, "rule_name": "test1", "match_time": "2019-02-16T02:53:57.142648Z", "alert_time": "2019-02-16T02:54:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:44:57.353993Z", "alert_sent": true, "match_body": {"reference_count": 0, "num_hits": 0, "@timestamp": "2019-02-16T02:43:57.021446Z", "spike_count": 0, "num_matches": 1}, "rule_name": "test1", "match_time": "2019-02-16T02:43:57.021446Z", "alert_time": "2019-02-16T02:44:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:34:57.243121Z", "alert_sent": true, "match_body": {"reference_count": 0, "num_hits": 0, "@timestamp": "2019-02-16T02:33:57.010926Z", "spike_count": 0, "num_matches": 1}, "rule_name": "test1", "match_time": "2019-02-16T02:33:57.010926Z", "alert_time": "2019-02-16T02:34:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:24:57.220362Z", "alert_sent": true, "match_body": {"reference_count": 0, "num_hits": 0, "@timestamp": "2019-02-16T02:23:56.984979Z", "spike_count": 0, "num_matches": 1}, "rule_name": "test1", "match_time": "2019-02-16T02:23:56.984979Z", "alert_time": "2019-02-16T02:24:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:26.102912Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 0, "@timestamp": "2019-02-16T02:13:37.167920Z", "spike_count": 0, "num_matches": 1}, "rule_name": "test1", "match_time": "2019-02-16T02:13:37.167920Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:26.066478Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-16T02:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-16T02:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:26.031001Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-16T01:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-16T01:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.998312Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-16T01:43:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-16T01:43:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.961850Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-16T01:33:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-16T01:33:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.904683Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-16T01:23:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-16T01:23:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.867255Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-16T01:13:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-16T01:13:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.832071Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-16T01:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-16T01:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.795358Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-16T00:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-16T00:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.752517Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-16T00:43:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-16T00:43:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.719501Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-16T00:33:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-16T00:33:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.682887Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-16T00:23:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-16T00:23:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.652116Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-16T00:13:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-16T00:13:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.609945Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-16T00:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-16T00:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.577301Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T23:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T23:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.491568Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T23:43:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T23:43:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.411045Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T23:33:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T23:33:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.379288Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T23:23:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T23:23:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.339112Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T23:13:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T23:13:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.302322Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T23:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T23:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.262436Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T22:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T22:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.227564Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T22:43:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T22:43:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.192181Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T22:33:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T22:33:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.161249Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T22:23:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T22:23:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.125076Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T22:13:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T22:13:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.090237Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T22:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T22:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.047320Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T21:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T21:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:25.015292Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T21:43:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T21:43:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.981454Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T21:33:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T21:33:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.949681Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T21:23:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T21:23:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.916944Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T21:13:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T21:13:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.882211Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T21:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T21:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.849300Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T20:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T20:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.811882Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T20:43:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T20:43:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.749238Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T20:33:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T20:33:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.710038Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T20:23:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T20:23:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.670396Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T20:13:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T20:13:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.635284Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T20:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T20:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.603509Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T19:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T19:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.567364Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T19:43:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T19:43:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.535441Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T19:33:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T19:33:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.505336Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T19:23:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T19:23:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.473360Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T19:13:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T19:13:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.438848Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T19:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T19:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.407746Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T18:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T18:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.373061Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T18:43:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T18:43:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.339454Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T18:33:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T18:33:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.300281Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T18:23:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T18:23:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.269864Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T18:13:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T18:13:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.235969Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T18:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T18:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.197367Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T17:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T17:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.164841Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T17:43:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T17:43:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.129412Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T17:33:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T17:33:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.096307Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T17:23:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T17:23:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.063718Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T17:13:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T17:13:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:24.025400Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T17:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T17:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.995194Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T16:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T16:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.965521Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T16:43:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T16:43:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.932078Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T16:33:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T16:33:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.900078Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T16:23:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T16:23:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.867033Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T16:13:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T16:13:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.835445Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T16:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T16:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.801148Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T15:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T15:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.767622Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T15:43:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T15:43:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.732974Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T15:33:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T15:33:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.697952Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T15:23:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T15:23:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.664377Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T15:13:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T15:13:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.635219Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T15:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T15:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.599268Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T14:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T14:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.564863Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T14:43:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T14:43:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.478812Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T14:33:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T14:33:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.444837Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T14:23:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T14:23:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.407965Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T14:13:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T14:13:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.374817Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T14:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T14:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.334857Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T13:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T13:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.302121Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T13:43:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T13:43:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.269251Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T13:33:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T13:33:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.238807Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T13:23:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T13:23:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.206024Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T13:13:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T13:13:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.169978Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T13:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T13:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.134120Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T12:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T12:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.103064Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T12:43:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T12:43:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.071588Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T12:33:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T12:33:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:23.036951Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T12:23:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T12:23:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:22.999748Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T12:13:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T12:13:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:22.967329Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T12:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T12:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:22.924242Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T11:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T11:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:22.887374Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T11:43:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T11:43:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:22.847239Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T11:33:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T11:33:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:22.801736Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T11:23:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T11:23:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:22.768444Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T11:13:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T11:13:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:22.733056Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T11:03:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T11:03:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}, {"alert_info": {"slack_username_override": "Praeco", "type": "slack"}, "@timestamp": "2019-02-16T02:20:22.670591Z", "alert_sent": true, "aggregate_id": "AWj0GpojUXvdr6LFrRJG", "match_body": {"reference_count": 0, "num_hits": 3310, "@timestamp": "2019-02-15T10:53:00.620973Z", "spike_count": 0, "num_matches": 341}, "rule_name": "test1", "match_time": "2019-02-15T10:53:00.620973Z", "alert_time": "2019-02-16T02:19:00Z"}]}