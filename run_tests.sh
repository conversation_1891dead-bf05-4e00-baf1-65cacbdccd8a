#!/bin/sh

./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alertText.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/ConfigAlert.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/ConfigYaml.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlAlerta.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlAlerta002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlAlertaOpt.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlAlertmanager.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlAlertmanager002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlChatwork.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlCommand.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlDatadog.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlDingtalk.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlDingtalk002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlDingtalk003.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlDingtalk004.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlDiscord.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlDiscordOpt.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlEmail.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlEmail002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlExotel.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlGelf001.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlGelf002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlGitter.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlGitterOpt.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlGoogleChat.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlGoogleChat002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlHttpPost.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlHttpPost002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlHttpPostOpt.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlHttpPost2.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlHttpPost2Opt.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlIris.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlIris002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlJira.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlLark.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlMattermost.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlMattermost002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlMatrixhookshot001.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlMatrixhookshot002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlMsTeams.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlMsTeamsOpt.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlMsPowerAutomate001.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlMsPowerAutomate002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlOpsgenie.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlOpsgenie002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlOpsgenie003.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlOpsgenie004.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlOpsgenie005.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlOpsgenie006.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlPagerDuty.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlPagerDuty002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlPagerDuty003.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlPagerDuty004.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlPagerDuty005.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlPagerTree.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlRocketChat.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlRocketChat002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlServiceNow.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlSes.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlSesProfile.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlSlack.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlSlack002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlSns.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlSnsProfile.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlStomp.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlStompModRequiredAndOpt.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlTelegram001.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlTelegram002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlTelegram003.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlTencentSms.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlTencentSmsOpt.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlTheHive.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlTheHiveOpt.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlTwilioCopilot.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlTwilioSms.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlVictorOps.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlVictorOpsOpt.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlWorkWeChat.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlWorkWeChat002.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlZabbix.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/alert/ConfigYamlZabbixSenderHostPort.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/ConfigYamlScanEntireTimefame.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/ErrorsLog.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/luceneSyntaxBuilder.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/QueriesLog.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/RuleView.spec.js &&
./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/SilenceLog.spec.js

# TODO: Tests that fail
# ./node_modules/.bin/vue-cli-service test:unit tests/unit/specs/ConfigCondition.spec.js &&