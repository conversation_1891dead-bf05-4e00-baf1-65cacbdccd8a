<template>
  <div id="app">
    <el-header class="modern-header">
      <div class="header-content">
        <div class="header-brand">
          <router-link to="/" class="brand-link">
            <div class="brand-icon">
              <Icon icon="bell" />
            </div>
            <div class="brand-text">
              <h1 class="brand-title">Praeco</h1>
              <span class="brand-subtitle">ElastAlert Management</span>
            </div>
          </router-link>
        </div>

        <div class="header-center">
          <nav class="header-nav">
            <router-link
              to="/"
              class="nav-link"
              :class="{ active: $route.path === '/' }">
              <Icon icon="globe" />
              <span>Dashboard</span>
            </router-link>
            <router-link
              to="/rules"
              class="nav-link"
              :class="{ active: $route.path.startsWith('/rules') }">
              <Icon icon="file-alt" />
              <span>Rules</span>
            </router-link>
            <router-link
              to="/templates"
              class="nav-link"
              :class="{ active: $route.path.startsWith('/templates') }">
              <Icon icon="file" />
              <span>Templates</span>
            </router-link>
          </nav>
        </div>

        <div class="header-actions">
          <div class="status-indicator">
            <UpdateIndicator />
            <div class="status-badge" :class="statusClass">
              <div class="status-dot"></div>
              <span>{{ $store.state.server.status || 'Unknown' }}</span>
            </div>
          </div>
          <el-button class="action-button" circle>
            <Icon icon="question-circle" />
          </el-button>
        </div>
      </div>
    </el-header>

    <!-- Custom resizable split panel -->
    <div class="split-container" style="height: calc(100% - 48px);">
      <div
        class="modern-sidebar"
        :style="{ width: sidebarWidth + 'px' }">
        <div class="sidebar-header">
          <h3>Navigation</h3>
        </div>
        <div class="sidebar-content">
          <NavTree />
        </div>
      </div>
      <div
        class="resizer"
        @mousedown="startResize"
        style="width: 4px; background: #ddd; cursor: col-resize; user-select: none;">
      </div>
      <div class="modern-main-content">
        <div class="content-wrapper">
          <router-view :key="$route.fullPath" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import UpdateIndicator from '@/components/UpdateIndicator.vue';

export default {
  components: {
    UpdateIndicator
  },

  data() {
    return {
      isResizing: false
    };
  },

  computed: {
    sidebarWidth: {
      get() {
        return this.$store.state.ui.sidebarWidth[0] || 300;
      },
      set(value) {
        this.$store.commit('ui/UPDATE_SIDEBAR_WIDTH', [value, window.innerWidth - value]);
      }
    },

    statusClass() {
      const status = this.$store.state.server.status;
      if (status === 'running') return 'status-success';
      if (status === 'stopped') return 'status-error';
      return 'status-warning';
    }
  },

  mounted() {
    this.$store.dispatch('server/fetchVersion');
    this.$store.dispatch('server/fetchStatus');
    this.$store.dispatch('elastalert/fetchConfig');
  },

  methods: {
    startResize(e) {
      this.isResizing = true;
      document.addEventListener('mousemove', this.doResize);
      document.addEventListener('mouseup', this.stopResize);
      e.preventDefault();
    },

    doResize(e) {
      if (!this.isResizing) return;
      const newWidth = Math.max(200, Math.min(600, e.clientX));
      this.sidebarWidth = newWidth;
    },

    stopResize() {
      this.isResizing = false;
      document.removeEventListener('mousemove', this.doResize);
      document.removeEventListener('mouseup', this.stopResize);
    }
  }
};
</script>

<style lang="scss">
/* Modern App Layout */
#app {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Modern Header */
.modern-header {
  background: var(--gradient-surface);
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  height: 72px !important;
  padding: 0 !important;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 var(--space-6);
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.header-brand {
  display: flex;
  align-items: center;
}

.brand-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
}

.brand-link:hover {
  transform: translateY(-1px);
}

.brand-icon {
  width: 48px;
  height: 48px;
  background: var(--gradient-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin-right: var(--space-3);
  box-shadow: var(--shadow-md);
}

.brand-text {
  display: flex;
  flex-direction: column;
}

.brand-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.brand-subtitle {
  font-size: 0.875rem;
  color: var(--gray-600);
  font-weight: 500;
}

/* Header Navigation */
.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-nav {
  display: flex;
  gap: var(--space-2);
  background: var(--gray-100);
  padding: var(--space-1);
  border-radius: var(--radius-lg);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  text-decoration: none;
  color: var(--gray-600);
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--primary-600);
  background: var(--primary-50);
}

.nav-link.active {
  color: var(--primary-600);
  background: white;
  box-shadow: var(--shadow-sm);
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.status-badge {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  background: var(--gray-100);
  border: 1px solid var(--gray-200);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-success .status-dot {
  background: var(--success-500);
}

.status-error .status-dot {
  background: var(--error-500);
}

.status-warning .status-dot {
  background: var(--warning-500);
}

.action-button {
  width: 40px !important;
  height: 40px !important;
  border-radius: var(--radius-lg) !important;
  border: 1px solid var(--gray-200) !important;
  background: white !important;
  color: var(--gray-600) !important;
  transition: all 0.2s ease !important;
}

.action-button:hover {
  color: var(--primary-600) !important;
  border-color: var(--primary-200) !important;
  background: var(--primary-50) !important;
  transform: translateY(-1px) !important;
}

/* Modern Split Container */
.split-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 72px);
  background: var(--gray-50);
}

/* Modern Sidebar */
.modern-sidebar {
  background: white;
  border-right: 1px solid var(--gray-200);
  display: flex;
  flex-direction: column;
  min-width: 200px;
  max-width: 600px;
  box-shadow: var(--shadow-sm);
}

.sidebar-header {
  padding: var(--space-4) var(--space-4) var(--space-3);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--gray-700);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-3);
}

/* Modern Resizer */
.resizer {
  width: 4px;
  background: var(--gray-200);
  cursor: col-resize;
  user-select: none;
  transition: all 0.2s ease;
  position: relative;
}

.resizer:hover {
  background: var(--primary-400);
  width: 6px;
}

.resizer:active {
  background: var(--primary-500);
}

/* Modern Main Content */
.modern-main-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-6);
  background: var(--gray-50);
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 0 var(--space-4);
  }

  .header-nav {
    display: none;
  }

  .brand-subtitle {
    display: none;
  }

  .status-indicator span {
    display: none;
  }
}
</style>
