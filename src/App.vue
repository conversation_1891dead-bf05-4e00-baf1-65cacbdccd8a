<template>
  <div id="app">
    <el-header>
      <div id="nav">
        <el-row>
          <el-col :span="12">
            <router-link to="/">
              <img alt="praeco" src="@/assets/logo.png">
            </router-link>
          </el-col>
          <el-col :span="12" align="right">
            <p>
              <UpdateIndicator />
              <el-tag type="info" class="m-w-xs">
                elastalert status: {{ $store.state.server.status || '?' }}
              </el-tag>
            </p>
          </el-col>
        </el-row>
      </div>
    </el-header>

    <!-- Custom resizable split panel -->
    <div class="split-container" style="height: calc(100% - 48px);">
      <div
        class="sidebar"
        :style="{ width: sidebarWidth + 'px' }"
        style="background: #f8f8fb; overflow-y: auto;">
        <NavTree style="padding: 10px" />
      </div>
      <div
        class="resizer"
        @mousedown="startResize"
        style="width: 4px; background: #ddd; cursor: col-resize; user-select: none;">
      </div>
      <div class="main-content" style="flex: 1; overflow-y: auto;">
        <router-view :key="$route.fullPath" style="padding: 10px" />
      </div>
    </div>
  </div>
</template>

<script>
import UpdateIndicator from '@/components/UpdateIndicator.vue';

export default {
  components: {
    UpdateIndicator
  },

  data() {
    return {
      isResizing: false
    };
  },

  computed: {
    sidebarWidth: {
      get() {
        return this.$store.state.ui.sidebarWidth[0] || 300;
      },
      set(value) {
        this.$store.commit('ui/UPDATE_SIDEBAR_WIDTH', [value, window.innerWidth - value]);
      }
    }
  },

  mounted() {
    this.$store.dispatch('server/fetchVersion');
    this.$store.dispatch('server/fetchStatus');
    this.$store.dispatch('elastalert/fetchConfig');
  },

  methods: {
    startResize(e) {
      this.isResizing = true;
      document.addEventListener('mousemove', this.doResize);
      document.addEventListener('mouseup', this.stopResize);
      e.preventDefault();
    },

    doResize(e) {
      if (!this.isResizing) return;
      const newWidth = Math.max(200, Math.min(600, e.clientX));
      this.sidebarWidth = newWidth;
    },

    stopResize() {
      this.isResizing = false;
      document.removeEventListener('mousemove', this.doResize);
      document.removeEventListener('mouseup', this.stopResize);
    }
  }
};
</script>

<style lang="scss">
html,
body {
  height: 100%;
}

body {
  font-family: "Helvetica Neue", Arial, sans-serif;
  font-size: 14px;
  color: #303133;
}

#app,
.el-aside > .el-menu,
.el-main > section {
  height: 100%;
}

#app .el-container {
  height: calc(100% - 48px);
}

.el-header img {
  height: 41px;
  width: auto;
  opacity: 0.5;
}

#app > section > .el-aside {
  padding: 22px 0 20px 10px;
}

.el-header {
  background: #ddd;
  height: initial !important;
}

.split-container {
  display: flex;
  width: 100%;
}

.sidebar {
  min-width: 200px;
  max-width: 600px;
  border-right: 1px solid #e4e7ed;
}

.resizer {
  transition: background-color 0.2s;
}

.resizer:hover {
  background-color: #409eff !important;
}
</style>
