import { createApp } from 'vue';
import ElementPlus, { ElMessage } from 'element-plus';
import 'element-plus/dist/index.css';
import axios from 'axios';
import VueNativeSock from 'vue-native-websocket-vue3';
import VueSplit from 'vue-split-panel';
import VueJsonPretty from 'vue-json-pretty';
import 'vue-json-pretty/lib/styles.css';
import Prism from 'vue-prism-component';
import 'prismjs';
import 'prismjs/themes/prism.css';
import 'prismjs/components/prism-yaml.min.js';
import Treeselect from '@r2rka/vue3-treeselect';
import '@r2rka/vue3-treeselect/dist/style.css';
import { library } from '@fortawesome/fontawesome-svg-core';
import {
  faBell,
  faChevronDown,
  faChevronUp,
  faEllipsisH,
  faEnvelope,
  faGlobe,
  faFile,
  faFileAlt,
  faFolder,
  faFolderOpen,
  faQuestionCircle,
  faExclamationCircle
} from '@fortawesome/free-solid-svg-icons';
import {
  faSlack, faMicrosoft, faGitter, faAws, faLine, faTelegram, faJira, faRocketchat
} from '@fortawesome/free-brands-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import 'normalize.css';

import ECharts from 'vue-echarts';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { BarChart, LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  MarkPointComponent,
  MarkLineComponent
} from 'echarts/components';

// import cronLight from '@vue-js-cron/light';
// import '@vue-js-cron/light/dist/light.css';

// Register ECharts components
use([
  CanvasRenderer,
  BarChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  MarkPointComponent,
  MarkLineComponent
]);

// Import components
import Bulb from '@/components/Bulb.vue';
import DateTime from '@/components/DateTime.vue';
import DefinitionTable from '@/components/DefinitionTable.vue';
import ElastalertTimePicker from '@/components/ElastalertTimePicker.vue';
import ESChart from '@/components/ESChart.vue';
import EventTable from '@/components/EventTable.vue';
import ExpandableAlert from '@/components/ExpandableAlert.vue';
import FolderTree from '@/components/FolderTree.vue';
import NavTree from '@/components/NavTree.vue';
import PraecoFormItem from '@/components/PraecoFormItem.vue';
import TableRow from '@/components/TableRow.vue';
import ElastalertTimeView from '@/components/ElastalertTimeView.vue';
// Config components - these will be auto-imported by Vite
// We'll keep the imports for now but can remove them later with auto-import

import '@/lib/string.js';
import { initLogging, logger } from './lib/logger.js';
import App from './App.vue';
import router from './router';
import store from './store';

// Import styles
import './style/style.scss';
import './style/spacing.scss';
import './style/tree.scss';
import './style/icons.scss';
import './style/query-builder.scss';
import './style/element.scss';
import './style/pop-trigger.scss';

// Add FontAwesome icons to library
library.add(
  faBell,
  faFile,
  faFileAlt,
  faSlack,
  faRocketchat,
  faMicrosoft,
  faGitter,
  faAws,
  faLine,
  faTelegram,
  faJira,
  faGlobe,
  faEnvelope,
  faChevronUp,
  faChevronDown,
  faEllipsisH,
  faFolder,
  faFolderOpen,
  faQuestionCircle,
  faExclamationCircle
);

// Create Vue app
function createPraecoApp(config) {
  const app = createApp(App);

  // Configure app
  app.config.globalProperties.$appConfig = config;

  // Error handler
  app.config.errorHandler = function(err, vm, info) {
    logger().error(err);
    console.error(err, vm, info);

    // Use Element Plus notification
    ElMessage.error({
      message: err.toString(),
      title: 'Internal error',
      duration: 0
    });
  };

  // Use plugins
  app.use(ElementPlus, { size: 'small' });
  app.use(router);
  app.use(store);
  // app.use(cronLight);
  app.use(VueSplit);

  // Register global components
  app.component('VChart', ECharts);
  app.component('Icon', FontAwesomeIcon);
  app.component('VueJsonPretty', VueJsonPretty);
  app.component('Prism', Prism);
  app.component('Treeselect', Treeselect);

  // Register custom components
  app.component('Bulb', Bulb);
  app.component('DateTime', DateTime);
  app.component('DefinitionTable', DefinitionTable);
  app.component('ElastalertTimePicker', ElastalertTimePicker);
  app.component('ESChart', ESChart);
  app.component('EventTable', EventTable);
  app.component('ExpandableAlert', ExpandableAlert);
  app.component('FolderTree', FolderTree);
  app.component('NavTree', NavTree);
  app.component('PraecoFormItem', PraecoFormItem);
  app.component('TableRow', TableRow);
  app.component('ElastalertTimeView', ElastalertTimeView);

  return app;
}

function startApp(config) {
  // Set app config in store
  store.commit('appconfig/SET_APP_CONFIG', config);

  // Initialize logging
  initLogging();

  // Create the app
  const app = createPraecoApp(config);

  // Add WebSocket support
  app.use(VueNativeSock, `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.hostname}:${window.location.port}/api-ws/test`, {
    connectManually: true,
    format: 'json'
  });

  // Mount the app
  app.mount('#app');
}

// Load config and start app
axios.defaults.baseURL = import.meta.env.BASE_URL || '/';
axios
  .get('/praeco.config.json')
  .then(res => {
    startApp(res.data);
  })
  .catch(() => {
    alert('praeco.config.json missing');
  });
