.vue-treeselect__menu {
  padding: 0;
  border: 0;
  border-radius: 0 !important;
  box-shadow: none !important;
  position: inherit;
  background: transparent;
}

.vue-treeselect__control {
  display: none;
}

.vue-treeselect__menu-container {
  position: inherit;
}

.vue-treeselect__label {
  color: inherit;
}

.vue-treeselect__icon-container {
  display: none;
}

.vue-treeselect__no-children-tip-text {
  display: none;
}

.vjs__tree {
  font-family: inherit;
  font-size: inherit;
}

.vjs__tree .vjs__value__boolean,
.vjs__tree .vjs__value__number,
.vjs__tree .vjs__value__string {
  color: inherit;
}

.vue-treeselect__option--highlight {
  background: #efefef;
}
