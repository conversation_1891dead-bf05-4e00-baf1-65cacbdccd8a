.condition-view {
  .pop-trigger {
    border-bottom: 0 !important;
    cursor: default !important;
    pointer-events: none;
  }

  .pop-trigger-pseudo {
    pointer-events: none;
  }

  .pop-trigger > :first-child {
    color: #757575 !important;
  }
}

.pop-trigger {
  border-bottom: 1px dotted #157ce7;
  cursor: pointer;
  margin-right: 20px;
  font-size: 17px;
  line-height: 1.5;
}

.pop-trigger-pseudo {
  margin-right: 20px;
  font-size: 17px;
  line-height: 1.5;
}

.is-invalid .pop-trigger,
.is-invalid .pop-trigger-pseudo {
  border-bottom: 1px dotted #f56c6c;
}

.is-invalid .pop-trigger > :first-child,
.is-invalid .pop-trigger-pseudo > :first-child {
  color: #f56c6c;
}

.pop-trigger > :first-child {
  color: #157ce7;
}
