// Modern Design System Variables
:root {
  /* Element Plus Integration */
  --el-color-primary: #0ea5e9;
  --el-color-warning: #f59e0b;

  /* Primary Colors */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  /* Neutral Colors */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Success Colors */
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;

  /* Warning Colors */
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;

  /* Error Colors */
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-500) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--gray-700) 0%, var(--gray-600) 100%);
  --gradient-surface: linear-gradient(135deg, #ffffff 0%, var(--gray-50) 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;

  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

// Element Plus styles are imported in main.js

/* Modern Base Styles */
html {
  height: 100%;
}

body {
  margin: 0;
  height: 100%;
  font-family: var(--font-sans);
  background: var(--gray-50);
  color: var(--gray-900);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modern Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--gray-800);
  font-weight: 600;
  line-height: 1.25;
  margin: 0 0 var(--space-4) 0;
}

h1 {
  font-size: 2.25rem;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h2 {
  font-size: 1.875rem;
  color: var(--gray-700);
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

h5 {
  font-size: 1.125rem;
}

h6 {
  font-size: 1rem;
  color: var(--gray-600);
  margin-bottom: var(--space-2);
  margin-top: var(--space-3);
}

/* Modern Code Blocks */
pre {
  font-family: var(--font-mono);
  word-break: break-word;
  white-space: pre-wrap;
  margin: 0;
  font-size: 0.875rem;
  background: var(--gray-900);
  color: var(--gray-100);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-700);
  box-shadow: var(--shadow-md);
  overflow-x: auto;
}

/* Modern HR and Labels */
hr {
  border: 0;
  border-top: 1px solid var(--gray-200);
  margin: var(--space-6) 0;
}

label {
  color: var(--gray-700);
  font-weight: 500;
  font-size: 0.875rem;
}

/* Modern Drawer Components */
.drawer-row {
  padding-bottom: 240px;
}

.drawer-card {
  position: fixed;
  bottom: -30px;
  padding-bottom: 30px;
  height: 200px;
  width: calc(100% - 250px);
  background: var(--gradient-surface);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  box-shadow: var(--shadow-xl);
  z-index: 999;
  backdrop-filter: blur(10px);
}

.drawer-card-open {
  height: calc(100% - 30px);
  left: var(--space-3);
  right: var(--space-3);
  width: auto;
  border-radius: var(--radius-xl);
}

.drawer-card .el-card__body {
  padding: var(--space-4);
}

/* Modern Utility Classes */
.text-muted {
  color: var(--gray-500);
}

.text-primary {
  color: var(--primary-600);
}

.text-success {
  color: var(--success-600);
}

.text-warning {
  color: var(--warning-600);
}

.text-error {
  color: var(--error-600);
}

/* Modern Card Styles */
.modern-card {
  background: var(--gradient-surface);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: all 0.2s ease;
}

.modern-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Modern Button Enhancements */
.el-button {
  border-radius: var(--radius-md) !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.el-button--primary {
  background: var(--gradient-primary) !important;
  border: none !important;
}

.el-button--primary:hover {
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-lg) !important;
}