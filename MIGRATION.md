# Migration Guide: External Elasticsearch to Included Elasticsearch 8.18

This guide helps you migrate from using an external Elasticsearch instance to the new included Elasticsearch 8.18 setup.

## What Changed

- **Elasticsearch 8.18** is now included in the Docker Compose setup
- **No more PRAECO_ELASTICSEARCH environment variable** needed
- Services communicate via <PERSON><PERSON>'s internal DNS
- Elasticsearch data is persisted in a Docker volume

## Migration Steps

### 1. Backup Your Current Setup

Before migrating, backup your current rules and configuration:

```bash
# Backup your rules and configuration
cp -r rules rules_backup
cp -r config config_backup
cp docker-compose.yml docker-compose.yml.backup
```

### 2. Stop Current Services

```bash
docker-compose down
```

### 3. Update Docker Compose

Replace your `docker-compose.yml` with the new version that includes Elasticsearch 8.18.

### 4. Remove Environment Variable

If you have a `.env` file or export `PRAECO_ELASTICSEARCH` in your shell, you can remove it:

```bash
# Remove from .env file if it exists
sed -i '/PRAECO_ELASTICSEARCH/d' .env

# Or unset from current shell
unset PRAECO_ELASTICSEARCH
```

### 5. Start New Services

```bash
docker-compose up -d
```

### 6. Verify Setup

Check that all services are running:

```bash
# Check service status
docker-compose ps

# Check Elasticsearch health
curl http://localhost:9200/_cluster/health

# Check Praeco UI
curl http://localhost:8080
```

## Data Migration (Optional)

If you want to migrate data from your external Elasticsearch to the new included instance:

### Option 1: Using Elasticsearch Snapshot/Restore

1. Create a snapshot on your old Elasticsearch instance
2. Copy the snapshot to the new instance
3. Restore the snapshot

### Option 2: Using Logstash or Custom Scripts

Use Logstash or custom scripts to reindex data from the old instance to the new one.

### Option 3: Fresh Start

Simply start fresh with the new Elasticsearch instance. Your Praeco rules and configuration will remain intact.

## Configuration Notes

- **Elasticsearch Security**: Disabled by default for simplicity
- **Memory Settings**: Set to 512MB heap size (adjust in docker-compose.yml if needed)
- **Data Persistence**: Elasticsearch data is stored in a Docker volume named `elasticsearch_data`
- **Network**: All services communicate via Docker's internal network

## Troubleshooting

### Elasticsearch Won't Start

Check the logs:
```bash
docker-compose logs elasticsearch
```

Common issues:
- Insufficient memory (increase Docker memory limits)
- Port conflicts (ensure 9200 and 9300 are available)

### ElastAlert Can't Connect

Check that Elasticsearch is healthy:
```bash
docker-compose logs elastalert
curl http://localhost:9200/_cluster/health
```

### Data Volume Issues

If you need to reset the Elasticsearch data:
```bash
docker-compose down
docker volume rm praeco_elasticsearch_data
docker-compose up -d
```

## Rollback

If you need to rollback to the external Elasticsearch setup:

1. Stop services: `docker-compose down`
2. Restore your backup: `cp docker-compose.yml.backup docker-compose.yml`
3. Set the environment variable: `export PRAECO_ELASTICSEARCH=<your-es-ip>`
4. Start services: `docker-compose up -d`

## Benefits of the New Setup

- **Simplified deployment**: No need to manage external Elasticsearch
- **Version compatibility**: Guaranteed compatibility with Elasticsearch 8.18
- **Easier development**: Everything runs locally
- **Better isolation**: Services are contained within Docker network
- **Consistent environment**: Same setup across development and production
