# cache github api
proxy_cache_path /tmp/nginx_cache levels=1:2 keys_zone=github_api_cache:60m max_size=10g
                 inactive=60m use_temp_path=off;

server {
  listen 8080;
  #rewrite ^/my-path(/.*)$ $1 last;

  location /api {
      rewrite ^/api/?(.*)$ /$1 break;
      proxy_pass http://elastalert:3030/;
      proxy_http_version 1.1;
      proxy_buffering off;
  }

  location /api-ws {
      rewrite ^/api-ws/?(.*)$ /$1 break;
      proxy_pass http://elastalert:3333/;
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "Upgrade";
  }

  location /api-app/releases {
      resolver 127.0.0.11;
      set $releases_url https://api.github.com/repos/ServerCentral/praeco/releases;
      proxy_cache github_api_cache;
      proxy_pass $releases_url;
  }

  location / {
      root /var/www/html;
      try_files $uri $uri/ /index.html;
  }
}
